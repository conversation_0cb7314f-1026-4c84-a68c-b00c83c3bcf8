import { apiService } from "./api";
import { TCart, TCartListParams } from "../types/cart";

const ENDPOINT = "v1/admin/carts";

export const cartService = apiService.injectEndpoints({
  endpoints: (build) => ({
    listCarts: build.query<TReponsePaging<TCart>, TCartListParams>({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: TReponsePaging<TCart>) => {
        const data = rawResult.data?.map((item: TCart, index) => ({
          ...item,
          _rowIndex: index + 1,
        }));
        return { ...rawResult, data: data || [] };
      },
    }),
    getUserCart: build.query<TCart, string>({
      query: (userId: string) => {
        return {
          url: `${ENDPOINT}/user/${userId}`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TCart) => {
        return rawResult;
      },
    }),
    getCartDetails: build.query<TCart, string>({
      query: (cartId: string) => {
        return {
          url: `${ENDPOINT}/${cartId}`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TCart) => {
        return rawResult;
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyListCartsQuery,
  useLazyGetUserCartQuery,
  useLazyGetCartDetailsQuery,
} = cartService;
