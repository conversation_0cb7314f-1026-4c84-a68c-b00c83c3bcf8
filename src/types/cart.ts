export interface TCartItem {
  id: string;
  cartSectionId: string;
  productId: string;
  variantId: string;
  productName: string;
  variantName: string;
  image: string;
  sku: string;
  quantity: number;
  rawPrice: string;
  price: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  bundleDiscountId: string | null;
  product: {
    id: string;
    shopifyProductId: string;
    title: string;
    description: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    handle: string;
    status: string;
    deletedAt: string | null;
    price: string;
    vendorId: string;
    productTypeId: string;
    categoryId: string;
    pickupOnly: number;
    fulfilProductId: string | null;
    classification: string | null;
    isGift: number;
    pendingChanges: string | null;
    pendingApproval: string | null;
    reviewSummary: {
      latestReviews: any[];
      totalReviews: number;
      averageRating: number;
      details: Record<string, number>;
    };
    onlineStoreUrl: string;
  };
  variant: {
    id: string;
    productId: string;
    shopifyVariantId: string;
    title: string;
    price: string;
    compareAtPrice: string | null;
    sku: string;
    legacyResourceId: string;
    position: number;
    inventoryQuantity: number;
    maxQuantity: number | null;
    inventoryPolicy: string;
    inventoryManagement: string;
    weight: number | null;
    weightUnit: string | null;
    barcode: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    imageId: string | null;
    supplierId: string | null;
    fulfilVariantId: string | null;
    availableForSale: number;
    numberSold: number;
    warehouseInventories: any[];
  };
  discount: any | null;
}

export interface TCartSection {
  id: string;
  cartId: string;
  bundleId: string | null;
  title: string | null;
  section: string;
  quantity: number;
  rawTotal: string | null;
  total: string;
  rawPrice: string | null;
  price: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  bundle: any | null;
  cartItems: TCartItem[];
}

export interface TCart {
  id: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  user: {
    email: string;
    firstName: string;
    lastName: string;
    avatar: string | null;
    friendlyUrl: string | null;
    active: number;
    stripeId: string | null;
    loginCode: string | null;
    verifiedAt: string | null;
    deletedAt: string | null;
    loginCodeExpiredAt: string | null;
    lastLoginAt: string;
    createdAt: string;
    updatedAt: string;
    deviceToken: string;
    defaultAddressId: string | null;
    shopifyCustomerId: string;
    rewardPoints: number;
    birthday: string | null;
    gender: string | null;
    phone: string;
    id: string;
    noDiscount: number;
    latitude: number | null;
    longitude: number | null;
    mile: number | null;
    timezone: string | null;
    avatarId: string | null;
    locale: string;
    smileId: number;
    coverId: string | null;
    shareUrl: string | null;
    socialProvider: string | null;
    socialId: string | null;
    receivePostNotifications: number;
    vendorId: string;
    avatarUrl: string | null;
    fullname: string;
  };
  cartSections: TCartSection[];
  itemCount: number;
  totalValue: number;
  lastActivity: string;
}

export interface TCartListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  userId?: string;
  dateFrom?: string;
  dateTo?: string;
}
