import { FC, Fragment, useEffect, useState, useCallback } from "react";
import { <PERSON>ert, Button, Form, InputGroup, Modal } from "react-bootstrap";
import { EPaymentType } from "../enums/payment-type-enum";
import countries from 'i18n-iso-countries';
import enLocale from 'i18n-iso-countries/langs/en.json';
import formatUSD from "../../utils/currency-formatter";

interface PaymentFormProps {
  show: boolean;
  balance?: number;
  paymentMethods: TPaymentMethod[];
  err: any;
  handleClose: (success: boolean) => void;
  submitPayment: (paymentMethodId: string, amount: number, note: string) => void;
}

const PaymentForm: FC<PaymentFormProps> = ({ show, balance, paymentMethods, err, handleClose, submitPayment }) => {
  const [amount, setAmount] = useState<number>(0);
  const [amountError, setAmountError] = useState<string>('');
  const [note, setNote] = useState<string>('');
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState<string>('');

  const handleAmountChange = (event: any) => {
    if (event) {
      event.preventDefault();
    }
    setAmount(event.target.value);
  };

  const handleNoteChange = (event: any) => {
    if (event) {
      event.preventDefault();
    }
    setNote(event.target.value);
  };

  const validate = () => {
    let newAmountError = '';
    if (isNaN(amount)) {
      newAmountError = 'Invalid number';
    }
    if (amount <= 0) {
      newAmountError = 'Amount must be positive';
    }
    if (balance !== null && balance !== undefined && amount > balance) {
      newAmountError = 'Amount cannot be more than balance';
    }
    setAmountError(newAmountError);
    return newAmountError.length === 0;
  };

  const handleSubmitPayment = useCallback((e) => {
    if (e) e.preventDefault()
    if (!validate()) return;
    submitPayment(selectedPaymentMethodId, amount, note);
  }, [amount, note])

  useEffect(() => {
    countries.registerLocale(enLocale);
  }, []);

  useEffect(() => {
    if (!selectedPaymentMethodId) {
      const defaultPaymentMethod = paymentMethods.find((pm) => pm.isDefault === true);
      if (defaultPaymentMethod) {
        setSelectedPaymentMethodId(defaultPaymentMethod.id);
      }
    }
  }, [paymentMethods]);

  useEffect(() => {
    setAmount(0)
    setNote('')
  }, [show])

  return (
    <Modal
      show={show}
      onHide={() => handleClose(false)}
      size="lg"
      aria-labelledby="contained-modal-title-vcenter"
      centered
    >
      <Form className="" onSubmit={handleSubmitPayment}>
        <Modal.Header closeButton>
          <Modal.Title id="contained-modal-title-vcenter">
            Payout
          </Modal.Title>
        </Modal.Header>

        <Modal.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          {
            (balance !== null && balance !== undefined) &&
            (
              <p className="fs-15">
                <strong>Balance:</strong> {formatUSD(balance ?? 0)}
              </p>
            )
          }

          <Form.Group className="mb-4">
            <Form.Label>Amount</Form.Label>
            <Form.Control
              type="text"
              name="amount"
              required
              placeholder="0"
              value={amount}
              onChange={handleAmountChange}
              className="form-control mb-2 border"
              isInvalid={amountError.length > 0}
            />
            <Form.Control.Feedback type="invalid">
              {amountError}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group>
            <Form.Label>Note: </Form.Label>
            <Form.Control
              as="textarea"
              rows={5}
              name="note"
              required
              placeholder="Payment description"
              value={note}
              onChange={handleNoteChange}
              className="form-control mb-2 border"
              isInvalid={amountError.length > 0}
            />
          </Form.Group>

          <Form.Group className="mb-4">
            <Form.Label>Payment Method</Form.Label>

            {
              (paymentMethods && paymentMethods.length > 0) ?
                (
                  <InputGroup>
                    {
                      paymentMethods.map((paymentMethod) => (
                        <InputGroup.Text key={Math.random()}>
                          <span className="input-group-addon">
                            <input type="radio" checked={selectedPaymentMethodId === paymentMethod.id} onChange={(event) => {
                              if (event.target.checked) {
                                setSelectedPaymentMethodId(paymentMethod.id)
                              }
                            }} /> {paymentMethod.paymentTypeName} {paymentMethod.isDefault ? '(Default)' : ''}</span>
                        </InputGroup.Text>
                      ))
                    }
                  </InputGroup>
                ) :
                (<p className="fs-14 text-muted">No payment method is available</p>)
            }
          </Form.Group>

          <Form.Group className="mb-4" style={{ height: '170px' }}>
            {
              (paymentMethods && paymentMethods.length > 0) &&
              (
                <Fragment>
                  {
                    paymentMethods
                      .filter(paymentMethod => paymentMethod.id === selectedPaymentMethodId)
                      .map(paymentMethod => (
                        <Fragment key={Math.random()}>
                          {
                            (paymentMethod.paymentType == EPaymentType.PAYPAL) &&
                            (
                              <ul className="list-unstyled order-details-list">
                                <li>
                                  <span className="me-2 text-default fw-semibold">PayPal Email:</span>
                                  <span className="fs-14 text-muted">{paymentMethod.paypalDetail.paypalEmail}</span>
                                </li>
                                {
                                  (paymentMethod.paypalDetail.legalName) &&
                                  (
                                    <li>
                                      <span className="me-2 text-default fw-semibold">Full Legal Name:</span>
                                      <span className="fs-14 text-muted">{paymentMethod.paypalDetail.legalName}</span>
                                    </li>
                                  )
                                }
                                {
                                  (paymentMethod.paypalDetail.countryCode) &&
                                  (
                                    <li>
                                      <span className="me-2 text-default fw-semibold">Country:</span>
                                      <span className="fs-14 text-muted">{countries.getName(paymentMethod.paypalDetail.countryCode, 'en')}</span>
                                    </li>
                                  )
                                }
                              </ul>
                            )
                          }
                          {
                            (paymentMethod.paymentType == EPaymentType.DIRECT_DEPOSIT) &&
                            (
                              <ul className="list-unstyled order-details-list">
                                <li>
                                  <span className="me-2 text-default fw-semibold">Routing Number:</span>
                                  <span className="fs-14 text-muted">{paymentMethod.directDepositDetail.routingNumber}</span>
                                </li>
                                <li>
                                  <span className="me-2 text-default fw-semibold">Account Name:</span>
                                  <span className="fs-14 text-muted">{paymentMethod.directDepositDetail.accountName}</span>
                                </li>
                                <li>
                                  <span className="me-2 text-default fw-semibold">Account Number:</span>
                                  <span className="fs-14 text-muted">{paymentMethod.directDepositDetail.accountNumber}</span>
                                </li>
                                <li>
                                  <span className="me-2 text-default fw-semibold">Account Type:</span>
                                  <span className="fs-14 text-muted">{paymentMethod.directDepositDetail.accountType}</span>
                                </li>
                                {
                                  (paymentMethod.directDepositDetail.bankName) &&
                                  (
                                    <li>
                                      <span className="me-2 text-default fw-semibold">Bank Name:</span>
                                      <span className="fs-14 text-muted">{paymentMethod.directDepositDetail.bankName}</span>
                                    </li>
                                  )
                                }
                                {
                                  (paymentMethod.directDepositDetail.accountHolderAddress) &&
                                  (
                                    <li>
                                      <span className="me-2 text-default fw-semibold">Account Holder Address:</span>
                                      <span className="fs-14 text-muted">{paymentMethod.directDepositDetail.accountHolderAddress}</span>
                                    </li>
                                  )
                                }
                              </ul>
                            )
                          }
                          {
                            (paymentMethod.paymentType == EPaymentType.ACH_TRANSFER) &&
                            (
                              <ul className="list-unstyled order-details-list">
                                <li>
                                  <span className="me-2 text-default fw-semibold">Routing Number:</span>
                                  <span className="fs-14 text-muted">{paymentMethod.achTransferDetail.routingNumber}</span>
                                </li>
                                <li>
                                  <span className="me-2 text-default fw-semibold">Account Name:</span>
                                  <span className="fs-14 text-muted">{paymentMethod.achTransferDetail.accountName}</span>
                                </li>
                                <li>
                                  <span className="me-2 text-default fw-semibold">Account Number:</span>
                                  <span className="fs-14 text-muted">{paymentMethod.achTransferDetail.accountNumber}</span>
                                </li>
                                <li>
                                  <span className="me-2 text-default fw-semibold">Account Type:</span>
                                  <span className="fs-14 text-muted">{paymentMethod.achTransferDetail.accountType}</span>
                                </li>
                              </ul>
                            )
                          }
                          {
                            (paymentMethod.paymentType == EPaymentType.OTHER) &&
                            (
                              <ul className="list-unstyled order-details-list">
                                <li>
                                  <span className="me-2 text-default fw-semibold">Note:</span>
                                  <span className="fs-14 text-muted">{paymentMethod.otherPaymentDetail.note}</span>
                                </li>
                              </ul>
                            )
                          }
                        </Fragment>
                      ))
                  }
                </Fragment>
              )
            }
          </Form.Group>
        </Modal.Body>

        <Modal.Footer>
          <Button
            variant="outline-primary"
            onClick={() => handleClose(false)}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            style={{ width: '90px' }}
            disabled={paymentMethods?.length === 0 || amount <= 0}
            type='submit'
          >
            Pay
          </Button>
        </Modal.Footer>

      </Form>
    </Modal>
  );
}

export default PaymentForm;