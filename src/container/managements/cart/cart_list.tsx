import React, { FC, Fragment, useEffect, useState } from "react";
import { Badge, Button, Card, Col, Form, Row, Table } from "react-bootstrap";
import { Link } from "react-router-dom";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useLazyListCartsQuery } from "../../../../services/cart";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import { debounce } from "lodash";
import moment from "moment";
import { currencySymbol } from "../../../../utils/constant/currency";
import { TCart } from "../../../../types/cart";

interface ManagementCartProps {}

const ManagementCart: FC<ManagementCartProps> = () => {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [lastPage, setLastPage] = useState(20);
  const [total, setTotal] = useState(20);
  const [search, setSearch] = useState("");
  const setDebouncedSearch = debounce((value) => setSearch(value), 1000);

  const [isLoading, setIsLoading] = useState(false);

  const [trigger] = useLazyListCartsQuery();

  const [carts, setCarts] = useState<TCart[]>([]);

  useEffect(() => {
    setIsLoading(true);
    trigger({ page, limit, search })
      .unwrap()
      .then((res) => {
        setCarts(res.data || []);
        setLastPage(res?.meta?.lastPage);
        setTotal(res?.meta?.total);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [page, limit, search]);

  const formatCurrency = (amount: number | string) => {
    const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
    return `${currencySymbol}${numAmount.toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    return moment(dateString).format("MMM DD, YYYY HH:mm");
  };

  const getCustomerName = (cart: TCart) => {
    const { firstName, lastName, fullname } = cart.user;
    if (fullname && fullname.trim()) {
      return fullname;
    }
    return `${firstName} ${lastName}`.trim() || "N/A";
  };

  const getCartItemsSummary = (cart: TCart) => {
    if (cart.cartSections.length === 0) return "No items";

    const totalItems = cart.cartSections.reduce(
      (sum, section) => sum + section.quantity,
      0
    );
    const uniqueProducts = cart.cartSections.reduce(
      (sum, section) => sum + section.cartItems.length,
      0
    );

    return `${totalItems} items (${uniqueProducts} products)`;
  };

  const getCartStatus = (cart: TCart) => {
    if (cart.itemCount === 0) return "Empty";
    if (cart.totalValue === 0) return "Pending";
    return "Active";
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Active":
        return "success";
      case "Pending":
        return "warning";
      case "Empty":
        return "secondary";
      default:
        return "info";
    }
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack title="Cart Management" route="" />
            </Card.Header>
            <Card.Body className="overflow-auto">
              <div className="app-container">
                <Form.Group className="mb-3">
                  <Form.Control
                    type="search"
                    placeholder="Search by user email or name..."
                    onChange={(e) => setDebouncedSearch(e.target.value)}
                  />
                </Form.Group>

                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th className="text-center">Cart ID</th>
                      <th className="text-center">Customer</th>
                      <th className="text-center">Items</th>
                      <th className="text-center">Total Value</th>
                      <th className="text-center">Status</th>
                      <th className="text-center">Last Activity</th>
                      <th className="text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {carts.map((cart: TCart) => (
                      <tr key={cart.id}>
                        <td className="text-center">
                          <span className="fw-semibold text-primary">
                            {cart.id.substring(0, 8)}...
                          </span>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <div className="me-2">
                              <div className="avatar avatar-sm bg-primary rounded-circle">
                                <span className="avatar-initials">
                                  {getCustomerName(cart)
                                    .charAt(0)
                                    .toUpperCase()}
                                </span>
                              </div>
                            </div>
                            <div>
                              <div className="fw-semibold">
                                {getCustomerName(cart)}
                              </div>
                              <div className="text-muted small">
                                {cart.user.email}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="text-center">
                          <div className="d-flex align-items-center justify-content-center">
                            <div className="me-2">
                              <i className="bx bx-package text-primary"></i>
                            </div>
                            <div>
                              <div className="fw-semibold">
                                {cart.itemCount}
                              </div>
                              <div className="text-muted small">
                                {getCartItemsSummary(cart)}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="text-center">
                          <div className="fw-semibold text-success">
                            {formatCurrency(cart.totalValue)}
                          </div>
                          {cart.cartSections.length > 0 && (
                            <div className="text-muted small">
                              {cart.cartSections.length} sections
                            </div>
                          )}
                        </td>
                        <td className="text-center">
                          <Badge
                            bg={getStatusBadgeVariant(getCartStatus(cart))}
                          >
                            {getCartStatus(cart)}
                          </Badge>
                        </td>
                        <td className="text-center">
                          <div className="text-muted">
                            {formatDate(cart.lastActivity)}
                          </div>
                        </td>
                        <td className="text-center">
                          <div className="btn-group">
                            <Button
                              variant="outline-primary"
                              size="sm"
                              as={Link}
                              to={`/managements-carts/${cart.id}`}
                              disabled={
                                !hasPermission(ACTION.READ, RESOURCE.ORDER)
                              }
                            >
                              <i className="bx bx-show me-1"></i>
                              View
                            </Button>
                            <Button
                              variant="outline-info"
                              size="sm"
                              as={Link}
                              to={`/managements-carts/user/${cart.userId}`}
                              disabled={
                                !hasPermission(ACTION.READ, RESOURCE.ORDER)
                              }
                            >
                              <i className="bx bx-user me-1"></i>
                              User Cart
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>

                <PaginationBar
                  page={page}
                  setPage={setPage}
                  lastPage={lastPage}
                  total={total}
                />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default ManagementCart;
