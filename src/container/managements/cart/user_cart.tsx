import React, { FC, Fragment, useEffect, useState } from "react";
import { Badge, Button, Card, Col, Row, Table } from "react-bootstrap";
import { Link, useParams, useNavigate } from "react-router-dom";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import Card<PERSON>eaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useLazyGetUserCartQuery } from "../../../../services/cart";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import moment from "moment";
import { currencySymbol } from "../../../../utils/constant/currency";
import { TCart } from "../../../../types/cart";

interface ManagementUserCartProps {}

const ManagementUserCart: FC<ManagementUserCartProps> = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const [cart, setCart] = useState<TCart | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const [trigger] = useLazyGetUserCartQuery();

  useEffect(() => {
    if (!userId) return;

    setIsLoading(true);
    trigger(userId)
      .unwrap()
      .then((res) => {
        setCart(res);
      })
      .catch(() => {
        navigate("/managements-carts");
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [userId, trigger, navigate]);

  const formatCurrency = (amount: number | string) => {
    const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
    return `${currencySymbol}${numAmount.toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    return moment(dateString).format("MMM DD, YYYY HH:mm:ss");
  };

  const getCustomerName = (cart: TCart) => {
    const { firstName, lastName, fullname } = cart.user;
    if (fullname && fullname.trim()) {
      return fullname;
    }
    return `${firstName} ${lastName}`.trim() || "N/A";
  };

  const getCartStatus = (cart: TCart) => {
    const itemCount =
      cart.itemCount ||
      cart.cartSections.reduce((sum, section) => sum + section.quantity, 0);
    const totalValue =
      cart.totalValue ||
      cart.cartSections.reduce(
        (sum, section) => sum + parseFloat(section.total),
        0
      );

    if (itemCount === 0) return "Empty";
    if (totalValue === 0) return "Pending";
    return "Active";
  };

  const getCalculatedItemCount = (cart: TCart) => {
    return (
      cart.itemCount ||
      cart.cartSections.reduce((sum, section) => sum + section.quantity, 0)
    );
  };

  const getCalculatedTotalValue = (cart: TCart) => {
    return (
      cart.totalValue ||
      cart.cartSections.reduce(
        (sum, section) => sum + parseFloat(section.total),
        0
      )
    );
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Active":
        return "success";
      case "Pending":
        return "warning";
      case "Empty":
        return "secondary";
      default:
        return "info";
    }
  };

  if (!cart) {
    return null;
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title={`User Cart - ${getCustomerName(cart)}`}
                route="/managements-carts"
              />
            </Card.Header>
            <Card.Body>
              {/* User and Cart Summary */}
              <Row className="mb-4">
                <Col md={6}>
                  <Card className="border">
                    <Card.Body>
                      <h6 className="card-title">User Information</h6>
                      <div className="row g-3">
                        <div className="col-12">
                          <label className="form-label text-muted">
                            Full Name
                          </label>
                          <div className="fw-semibold">
                            {getCustomerName(cart)}
                          </div>
                        </div>
                        <div className="col-12">
                          <label className="form-label text-muted">Email</label>
                          <div className="text-muted">{cart.user.email}</div>
                        </div>
                        <div className="col-6">
                          <label className="form-label text-muted">
                            User ID
                          </label>
                          <div className="text-muted font-monospace">
                            {cart.userId}
                          </div>
                        </div>
                        <div className="col-6">
                          <label className="form-label text-muted">Phone</label>
                          <div className="text-muted">
                            {cart.user.phone || "N/A"}
                          </div>
                        </div>
                        <div className="col-6">
                          <label className="form-label text-muted">
                            Reward Points
                          </label>
                          <div className="fw-semibold text-success">
                            {cart.user.rewardPoints || 0}
                          </div>
                        </div>
                        <div className="col-6">
                          <label className="form-label text-muted">
                            Last Login
                          </label>
                          <div className="text-muted">
                            {cart.user.lastLoginAt
                              ? formatDate(cart.user.lastLoginAt)
                              : "Never"}
                          </div>
                        </div>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
                <Col md={6}>
                  <Card className="border">
                    <Card.Body>
                      <h6 className="card-title">Cart Summary</h6>
                      <div className="row g-3">
                        <div className="col-6">
                          <label className="form-label text-muted">
                            Cart ID
                          </label>
                          <div className="fw-semibold text-primary">
                            {cart.id.substring(0, 8)}...
                          </div>
                        </div>
                        <div className="col-6">
                          <label className="form-label text-muted">
                            Status
                          </label>
                          <div>
                            <Badge
                              bg={getStatusBadgeVariant(getCartStatus(cart))}
                            >
                              {getCartStatus(cart)}
                            </Badge>
                          </div>
                        </div>
                        <div className="col-6">
                          <label className="form-label text-muted">
                            Total Items
                          </label>
                          <div className="fw-semibold">
                            {getCalculatedItemCount(cart)}
                          </div>
                        </div>
                        <div className="col-6">
                          <label className="form-label text-muted">
                            Total Value
                          </label>
                          <div className="fw-semibold text-success">
                            {formatCurrency(getCalculatedTotalValue(cart))}
                          </div>
                        </div>
                        <div className="col-12">
                          <label className="form-label text-muted">
                            Last Activity
                          </label>
                          <div className="text-muted">
                            {formatDate(cart.lastActivity)}
                          </div>
                        </div>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>

              {/* Cart Items */}
              {cart.cartSections.length > 0 ? (
                <div className="mb-4">
                  <h6 className="mb-3">Cart Contents</h6>
                  {cart.cartSections.map((section, sectionIndex) => (
                    <Card key={section.id} className="mb-3 border">
                      <Card.Header className="bg-light">
                        <div className="d-flex justify-content-between align-items-center">
                          <div>
                            <span className="fw-semibold text-capitalize">
                              {section.section}
                            </span>
                            <Badge bg="info" className="ms-2">
                              {section.quantity} items
                            </Badge>
                          </div>
                          <div className="fw-semibold text-success">
                            {formatCurrency(section.total)}
                          </div>
                        </div>
                      </Card.Header>
                      <Card.Body>
                        <Table className="table table-sm">
                          <thead>
                            <tr>
                              <th>Product</th>
                              <th>Variant</th>
                              <th className="text-center">SKU</th>
                              <th className="text-center">Quantity</th>
                              <th className="text-center">Unit Price</th>
                              <th className="text-center">Total</th>
                            </tr>
                          </thead>
                          <tbody>
                            {section.cartItems.map((item) => (
                              <tr key={item.id}>
                                <td>
                                  <div className="d-flex align-items-center">
                                    {item.image && (
                                      <img
                                        src={item.image}
                                        alt={item.productName}
                                        className="me-2"
                                        style={{
                                          width: "40px",
                                          height: "40px",
                                          objectFit: "cover",
                                          borderRadius: "4px",
                                        }}
                                      />
                                    )}
                                    <div>
                                      <div className="fw-semibold">
                                        {item.productName}
                                      </div>
                                      <div className="text-muted small">
                                        ID: {item.id}
                                      </div>
                                      {item.product?.description && (
                                        <div className="text-muted small mt-1">
                                          <div
                                            dangerouslySetInnerHTML={{
                                              __html:
                                                item.product.description.substring(
                                                  0,
                                                  100
                                                ) +
                                                (item.product.description
                                                  .length > 100
                                                  ? "..."
                                                  : ""),
                                            }}
                                          />
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </td>
                                <td>
                                  <span className="text-muted">
                                    {item.variantName}
                                  </span>
                                </td>
                                <td className="text-center">
                                  <span className="text-muted font-monospace small">
                                    {item.sku}
                                  </span>
                                </td>
                                <td className="text-center">
                                  <Badge bg="primary">{item.quantity}</Badge>
                                </td>
                                <td className="text-center">
                                  {formatCurrency(item.price)}
                                </td>
                                <td className="text-center fw-semibold">
                                  {formatCurrency(
                                    parseFloat(item.price) * item.quantity
                                  )}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </Table>
                      </Card.Body>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card className="border">
                  <Card.Body className="text-center text-muted">
                    <i className="bx bx-cart bx-lg mb-2"></i>
                    <div>This user's cart is empty</div>
                  </Card.Body>
                </Card>
              )}

              {/* Actions */}
              <div className="d-flex gap-2">
                <Button
                  variant="outline-secondary"
                  as={Link}
                  to="/managements-carts"
                >
                  <i className="bx bx-arrow-back me-1"></i>
                  Back to Carts
                </Button>
                <Button
                  variant="outline-primary"
                  as={Link}
                  to={`/managements-carts/${cart.id}`}
                  disabled={!hasPermission(ACTION.READ, RESOURCE.ORDER)}
                >
                  <i className="bx bx-show me-1"></i>
                  View Cart Details
                </Button>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default ManagementUserCart;
