import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "react-bootstrap";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import { useNavigate } from "react-router-dom";
import NewCommissionGroupDialog from "../../../../components/dialog/new-commission-group-dialog";
import { useAddCommissionGroupMutation, useDeleteCommissionGroupMutation, useLazyGetCommissionGroupsQuery } from "../../../../services/affiliation/affiliatie-tier-commission-group";
import { getAllErrorMessages } from "../../../../utils/errors";
import { ErrorType } from "../../../../utils/error_type";
import EditCommissionGroupDialog from "../../../../components/dialog/edit-commission-group-dialog";
import Swal from "sweetalert2";
import CommissionRangesTable from "../commission-ranges-table";
import { ECommissionGroupType } from "../../../../components/enums/commission_group_type";

interface CommissionConfigCardProps {
  tier: TAffiliateTier;
  setIsLoading: (show: boolean) => void;
}

const CommissionConfigCard: FC<CommissionConfigCardProps> = ({ tier, setIsLoading }) => {
  const [err, setErr] = useState<ErrorType>({});
  const [showNewCommissionGroupDialog, setShowNewCommissionGroupDialog] = useState<boolean>(false);
  const [showEditGroupDialog, setShowEditGroupDialog] = useState<boolean>(false);
  const [commissionGroups, setCommissionGroups] = useState<TAffiliateTierCommissionGroup[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<TAffiliateTierCommissionGroup>();

  const navigate = useNavigate();
  const [addCommissionGroup] = useAddCommissionGroupMutation();
  const [getCommissionGroups] = useLazyGetCommissionGroupsQuery();
  const [deleteCommissionGroup] = useDeleteCommissionGroupMutation();

  useEffect(() => {
    if (tier) {
      loadCommissionGroups();
    }
  }, []);

  const handleAddCommissionGroup = (type: ECommissionGroupType) => {
    setIsLoading(true);
    setErr({});
    addCommissionGroup({
      id: tier.id,
      type,
    })
      .unwrap()
      .then((res) => {
        setCommissionGroups([
          ...commissionGroups,
          res
        ]);
      })
      .catch((error) => setErr(getAllErrorMessages(error)))
      .finally(() => setIsLoading(false));
  }

  const loadCommissionGroups = () => {
    setIsLoading(true);
    setErr({});
    getCommissionGroups(tier.id)
      .unwrap()
      .then(setCommissionGroups)
      .catch((error) => setErr(getAllErrorMessages(error)))
      .finally(() => setIsLoading(false));
  }

  const handleDeleteGroup = (groupId: string) => {
    setIsLoading(true);
    deleteCommissionGroup(groupId)
      .unwrap()
      .then(() => {
        setCommissionGroups(commissionGroups.filter(grp => grp.id !== groupId));
      })
      .catch((error) => setErr(getAllErrorMessages(error)))
      .finally(() => setIsLoading(false));
  }

  const handleDeleteGroupButtonClick = (group: TAffiliateTierCommissionGroup) => {
    Swal.fire({
      title: "Are you sure?",
      html: `Delete ${group?.title || '[Untitled]'} commission group?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          handleDeleteGroup(group!.id);
        }
      })
      .catch((error) => {
        Swal.fire("Error!", error, "error");
      })
  }

  return (
    <Fragment>
      <Card className="custom-card">
        <Card.Header>
          <Card.Title>Commission Rates</Card.Title>
          <div className="px-2 justify-content-end">
            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION)}
              variant="primary-light"
              onClick={() => navigate("commission-products")}
              style={{ width: '260px' }}
            >
              <span className="ri-edit-line fs-14" /> Set Commissionable Products
            </Button>

            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION)}
              variant="primary-light"
              className="ms-2"
              onClick={() => { setShowNewCommissionGroupDialog(true) }}
            >
              <i className="bi bi-plus-lg" /> Add New Group
            </Button>
          </div>
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Accordion activeKey={commissionGroups.map((group) => group.id)} className="accordion accordion-primary" alwaysOpen>
            {
              commissionGroups.map((group) => (
                <Accordion.Item eventKey={group.id}>
                  <Accordion.Header>
                    {group.title}
                    <Button
                      hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION)}
                      variant="primary-light"
                      className="btn btn-sm ms-2"
                      onClick={() => {
                        setSelectedGroup(group);
                        setShowEditGroupDialog(true);
                      }}
                    >
                      <span className="ri-edit-line" />
                    </Button>
                    <Button
                      hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION) || commissionGroups.length <= 1}
                      variant="primary-light"
                      className="btn btn-sm btn-danger-light ms-2"
                      onClick={() => { handleDeleteGroupButtonClick(group) }}
                    >
                      <span className="bi bi-x-lg"></span>
                    </Button>
                  </Accordion.Header>
                  <Accordion.Body>
                    <CommissionRangesTable commissionGroupId={group.id} setIsLoading={setIsLoading} />
                  </Accordion.Body>
                </Accordion.Item>
              ))
            }
          </Accordion>
        </Card.Body>
      </Card>

      <NewCommissionGroupDialog
        isShow={showNewCommissionGroupDialog}
        onHide={() => { setShowNewCommissionGroupDialog(false) }}
        handleAddCommissionGroup={handleAddCommissionGroup}
      />

      {
        (selectedGroup) && <EditCommissionGroupDialog
          isShow={showEditGroupDialog}
          group={selectedGroup}
          onHide={() => {
            setShowEditGroupDialog(false);
            loadCommissionGroups();
          }}
          setIsLoading={setIsLoading}
        />
      }
    </Fragment>
  );
}

export default CommissionConfigCard;