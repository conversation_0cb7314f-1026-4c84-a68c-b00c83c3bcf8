import { FC, Fragment, useState } from "react";
import EditCommissionRateRangeDialog from "../dialogs/edit-commission-rate-range-dialog";
import { But<PERSON> } from "react-bootstrap";
import Swal from "sweetalert2";
import { getAllErrorMessages } from "../../../../utils/errors";
import { useDeleteCommissionRateMutation } from "../../../../services/affiliation/affiliatie-tier-commission-rate";
import formatNumber from "../../../../utils/number-formatter";

interface CommissionRateRowProps {
  commissionRate: TAffiliateTierCommissionRate;
  nextCommissionRate?: TAffiliateTierCommissionRate;
  setIsLoading: (isLoading: boolean) => void;
  onHide: (success: boolean) => void;
}

const CommissionRateRow: FC<CommissionRateRowProps> = ({ commissionRate, nextCommissionRate, setIsLoading, onHide }) => {
  const [showEditDialog, setShowEditDialog] = useState<boolean>(false);

  const [deleteCommissionRate] = useDeleteCommissionRateMutation();

  const handleEditDialogClose = (success: boolean) => {
    setShowEditDialog(false);
    onHide(success);
  }

  const handleDelete = async () => {
    Swal.fire({
      title: "Are you sure?",
      html: `<p>Do you want to delete this commission rate range?</p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setIsLoading(true);
          deleteCommissionRate(commissionRate.id)
            .unwrap()
            .then(() => {
              Swal.fire("Deleted!", "Commission rate range has been deleted.", "success");
              onHide(true);
            })
            .catch((error) => {
              const errorMessages = getAllErrorMessages(error);
              console.error("Error deleting commission rate range:", errorMessages);
              Swal.fire("Error!", errorMessages.messages[0], "error");
              onHide(false);
            })
            .finally(() => {
              setIsLoading(false);
            });
        }
      })
      .catch((error) => {
        const errorMessages = getAllErrorMessages(error);
        console.error("Error during deleting commission rate rage:", errorMessages);
        Swal.fire("Error!", errorMessages.messages[0], "error");
        onHide(false);
      });
  };

  return (
    <Fragment key={commissionRate.id}>
      <tr>
        <td>{formatNumber(commissionRate.revenueFrom)}</td>
        <td>{nextCommissionRate ? `< ${formatNumber(nextCommissionRate.revenueFrom)}` : <i className="bi bi-infinity" />}</td>
        <td>{formatNumber(commissionRate.commissionRate * 100)}</td>
        <td style={{ textAlign: "center" }}>
          <Button
            variant="primary-light"
            className="btn btn-warning-light btn-sm"
            onClick={() => { setShowEditDialog(true) }}
          >
            <span className="ri-edit-line fs-14"></span>
          </Button>

          <Button
            variant="primary-light"
            className="btn btn-danger-light btn-sm ms-3"
            onClick={handleDelete}
          >
            <span className="ri-delete-bin-7-line fs-14"></span>
          </Button>
        </td>
      </tr>

      <EditCommissionRateRangeDialog
        isShow={showEditDialog}
        commissionRate={commissionRate}
        setIsLoading={setIsLoading}
        onHide={handleEditDialogClose}
      />
    </Fragment>
  );
}

export default CommissionRateRow;