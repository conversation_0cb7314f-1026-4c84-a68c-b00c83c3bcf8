import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Table } from "react-bootstrap";
import { useLazyGetCommissionRatesQuery } from "../../../services/affiliation/affiliatie-tier-commission-rate";
import { ErrorType } from "../../../utils/error_type";
import { getAllErrorMessages } from "../../../utils/errors";
import NewCommissionRateRangeDialog from "./dialogs/new-commission-rate-range-dialog";
import CommissionRateRow from "./rows/commission-rate-row";

interface CommissionRangesTableProps {
  commissionGroupId: string;
  setIsLoading: (isLoading: boolean) => void;
}

const CommissionRangesTable: FC<CommissionRangesTableProps> = ({ commissionGroupId, setIsLoading }) => {
  const [err, setErr] = useState<ErrorType>();
  const [commissionRates, setCommisisonRates] = useState<TAffiliateTierCommissionRate[]>([]);
  const [showNewDialog, setShowNewDialog] = useState<boolean>(false);

  const [getCommissionRates] = useLazyGetCommissionRatesQuery();

  useEffect(() => {
    loadCommissionRates();
  }, []);

  const loadCommissionRates = () => {
    setIsLoading(true);
    setErr({});
    getCommissionRates(commissionGroupId)
      .unwrap()
      .then(setCommisisonRates)
      .catch((error) => { setErr(getAllErrorMessages(error)) })
      .finally(() => { setIsLoading(false) });
  }

  const handleNewDialogClose = (success: boolean) => {
    setShowNewDialog(false);
    if (success) {
      loadCommissionRates();
    }
  }

  const handleEditDialogClose = (success: boolean) => {
    if (success) {
      loadCommissionRates();
    }
  }

  return (
    <Fragment>
      <div className="text-end mb-2">
        <Button
          variant="primary"
          onClick={() => { setShowNewDialog(true) }}
        >
          <i className="bi bi-plus-lg" /> Add Range
        </Button>
      </div>

      {err?.messages?.map((message: string, index: number) => (
        <Alert key={index} variant="danger">
          {message}
        </Alert>
      ))}

      <Table className="table table-bordered text-nowrap border-bottom" responsive>
        <thead>
          <tr>
            <th style={{ width: "150px" }}>From ($)</th>
            <th style={{ width: "150px" }}>To ($)</th>
            <th>Commission Rate (%)</th>
            <th style={{ width: "150px", textAlign: "center" }}>Actions</th>
          </tr>
        </thead>
        <tbody>
          {
            commissionRates.map((rate, index) => (
              <CommissionRateRow
                commissionRate={rate}
                nextCommissionRate={(index < commissionRates.length - 1) ? commissionRates[index + 1] : undefined}
                setIsLoading={setIsLoading}
                onHide={handleEditDialogClose}
              />
            ))
          }
        </tbody>
      </Table>

      <NewCommissionRateRangeDialog
        isShow={showNewDialog}
        commissionGroupId={commissionGroupId}
        setIsLoading={setIsLoading}
        onHide={handleNewDialogClose}
      />
    </Fragment>
  );
}

export default CommissionRangesTable;