import React, {FC, useEffect, useRef, useState, Fragment, useMemo, useCallback} from "react"
import { use<PERSON>ara<PERSON>, useNavigate, Link } from "react-router-dom"
import {
    Button,
    Form,
    InputGroup,
    ListGroup,
    Spinner,
    Alert,
} from "react-bootstrap"
import {
    useGetChatMessagesQuery,
    useAnnotateChatMessageMutation,
    useLazyGetChatMessagesQuery,
    useTakeOverChatRoomMutation,
    useStopTakingOverChatRoomMutation,
    useCreateChatBotMessageMutation,
    useGetChatRoomByIdQuery,
    useLazyGetChatRoomQuery,
} from "../../../services/chatbot/chatbot.ts"

import { store } from "../../../services/rtk/store.ts"
import ChatRoomsList from "./chatRoomsList.tsx";
import {ChatMessage as IvsChatMessage, ChatRoom, ChatToken, SendMessageRequest} from "amazon-ivs-chat-messaging";
import {useCreateChatTokenMutation} from "../../../services/chat/chat.ts";

// TODO: Make this a separate component

interface ChatBubbleProps {
    message: ChatbotMessage
    annotation?: string
    onDoubleClick?: () => void
}

const ChatBubble: FC<ChatBubbleProps> = ({ message,annotation, onDoubleClick }) => {
    const {
        role,
        content,
        userName = "",
        sentAt,
        avatarUrl = "",
        userId = ""
    } = message;

    const isUser = role === "user";
    const timestamp = sentAt
        ? new Date(sentAt).toLocaleTimeString([], {
            hour: "numeric",
            minute: "2-digit",
        })
        : "";

    const profilePath = `/managements-users/details/${userId}`

    return (
        <div
            className={`d-flex mb-2 ${
                isUser ? "justify-content-start gap-1" : "justify-content-end gap-1"
            }`}
            style = {{
                opacity: message.pending ? 0.5 : 1,
                filter: message.pending? "blur(0.5px)" : "none"
            }}
            onDoubleClick={onDoubleClick}
        >
            {isUser ? (
                <>
                    {avatarUrl && (
                        <img
                            src={avatarUrl}
                            alt={`${userName} avatar`}
                            className="rounded-circle"
                            style={{ width: 32, height: 32, objectFit: "cover" }}
                        />
                    )}
                    <div className="d-flex flex-column">
                        <div className="mb-1">
                            <Link
                                to={profilePath}
                                className="fw-bold text-decoration-none text-reset"
                            >
                                {userName}
                            </Link>
                            <span className="text-muted small ms-2">{timestamp}</span>
                        </div>
                        <span
                            className="d-inline-block px-3 py-2 rounded-3 bg-primary text-white"
                            style={{ maxWidth: "75%" }}
                        >
              {content}
            </span>
                    </div>
                </>
            ) : (
                <>
                    <div className="d-flex flex-column align-items-end">
                        <div className="mb-1">
                            <span className="text-muted small">{timestamp}</span>
                            <span className="fw-bold ms-2">{userName}</span>
                        </div>
                        <span
                            className={`d-inline-block px-3 py-2 rounded-3 ${
                                role === "bot" ? "bg-light" : "bg-secondary text-white"
                            } text-end`}
                            style={{ maxWidth: "75%" }}
                        >
                            {content}
                        </span>

                        {annotation && (
                            <small className="text-muted fst-italic d-block mt-1 ms-1">
                                {annotation}
                            </small>
                        )}

                    </div>
                    {avatarUrl && (
                        <img
                            src={avatarUrl}
                            alt={`${userName} avatar`}
                            className="rounded-circle"
                            style={{ width: 32, height: 32, objectFit: "cover" }}
                        />
                    )}
                </>
            )}
        </div>
    );
}

const CHAT_PAGE_SIZE = 10
const ROOMS_PAGE_SIZE = 10

interface ChatbotMessage {
    avatarUrl?: string
    id: string
    content: string
    assistantId?: string | null
    adminId?: string | null
    userId?: string | null
    role: "user" | "bot" | "system" | "staff"
    sentAt?: string
    attributes?: Record<string, any>
    annotatedChatMessageId?: string | null
    _localAnnotation?: string
    userName?: string
    roomId: string
    pending?: boolean
    ivsChatMessageId?: string | null
}

interface ChatbotRoom {
    id: string
    name: string
    latestMessageAt: string
    userName: string
    lastMessage?: string
    isStaffResponding?: boolean,
    adminId?: string | null
}

const toMillis = (date?: string) => {
    const time = date ? Date.parse(date) : NaN
    return Number.isNaN(time) ? 0 : time
}

type PartialRoom = Pick<ChatbotRoom, "id"> &
    Partial<Omit<ChatbotRoom, "id">>

const Chatbot: FC = () => {
    const [chatPage, setChatPage] = useState(1)
    const [hasMoreChatPage, setHasMoreChatPage] = useState(true)
    const [chatLoading, setChatLoading] = useState(false)
    const [chatError, setChatError] = useState<string | null>(null)

    const [selectedRoom, setSelectedRoom] = useState<ChatbotRoom | null>(null)
    const [input, setInput] = useState("")
    const [messages, setMessages] = useState<ChatbotMessage[]>([])
    const [annotatingId, setAnnotatingId] = useState<string | null>(null)
    const [annotationText, setAnnotationText] = useState("")
    const scrollRef = useRef<HTMLDivElement>(null)
    const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true)
    const [isStaffResponding, setIsStaffResponding] = useState(false)
    const [takeOver,        { isLoading: takingOver }] = useTakeOverChatRoomMutation()
    const [stopTakingOver,  { isLoading: stopping }]  = useStopTakingOverChatRoomMutation()
    const currentAdmin = store.getState().auth?.user
    const currentAdminId = currentAdmin?.id ?? ""
    const [getChatMessages] = useLazyGetChatMessagesQuery()
    const previousScrollHeight = useRef<number>(0)
    const [ivsRoom, setIvsRoom] = useState<ChatRoom | null>(null)
    const [getOrCreateBotRoom, {isFetching: creatingBotChat}] = useLazyGetChatRoomQuery()
    const [isLoadingRoom, setIsLoadingRoom] = useState(false)
    const currentRoomIdRef = useRef<string>("")

    const chatWithBot = async () => {
        try {
            const room = await getOrCreateBotRoom().unwrap()
            await mountRoom(room)
            navigate(`/chatbot/${room.id}`, { replace: true })
        } catch (err) {
            console.error(err)
        }
    }

    const [getChatToken] = useCreateChatTokenMutation()

    const { roomId = ''} = useParams<{ roomId?: string }>() ?? {}
    const navigate   = useNavigate()

    const {
        data: fetchedRoom,
    } = useGetChatRoomByIdQuery(roomId!, { skip: !roomId })

    const tokenProvider = async (roomId: string): Promise<ChatToken> => {
        const token = await getChatToken(roomId).unwrap()
        return token
    }

    const getMessageKey = (message: ChatbotMessage): string => {
        return message.ivsChatMessageId || message.id
    }

    // Helper function to deduplicate messages
    const deduplicateMessages = (messages: ChatbotMessage[]): ChatbotMessage[] => {
        const seen = new Set<string>()
        return messages.filter(message => {
            const key = getMessageKey(message)
            if (seen.has(key)) {
                return false
            }
            seen.add(key)
            return true
        })
    }

    const mountRoom = async (room: PartialRoom) => {
        setIsLoadingRoom(true)

        currentRoomIdRef.current = room.id
        setSelectedRoom(room as ChatbotRoom);
        console.log('Setting', room)
        setMessages([]);
        setChatPage(1);
        setHasMoreChatPage(true);
        setShouldScrollToBottom(true);
        setIsStaffResponding(!!room.isStaffResponding);

        ivsRoom?.disconnect();
        setIvsRoom(null)

        const chatIVSRoom = new ChatRoom({
            regionOrUrl: import.meta.env.VITE_AWS_SNS_REGION,
            tokenProvider: () => tokenProvider(room.id),
        })
        setIvsRoom(chatIVSRoom)
        chatIVSRoom.connect()
        setIsLoadingRoom(false)
    }

    const refreshRoom = async () => {
        if (!selectedRoom) return
        await mountRoom(selectedRoom)
    }

    const toggleTakeOver = async () => {
        if (!selectedRoom) return
        try {
            if (isStaffResponding) {
                if (ivsRoom) {
                    const request = new SendMessageRequest("Zurno Customer Service has left the chat");
                    request.attributes = {
                        display_name: "System",
                        avatar_url: "",
                        type: "system",
                    }

                    try {
                        const response = await ivsRoom.sendMessage(request)
                        const { id: messageId } = response
                        const now = new Date().toISOString()
                        const systemMessage: ChatbotMessage = {
                            id: messageId,
                            roomId: selectedRoom.id,
                            content: "Zurno Customer Service has left the chat",
                            role: "system",
                            sentAt: now,
                            userName: "System",
                            avatarUrl: "",
                            pending: true,
                            ivsChatMessageId: messageId
                        }

                        setMessages((prev) => {
                            const exists = prev.some((message) => getMessageKey(message) === getMessageKey(systemMessage))
                            return exists ? prev : [...prev, systemMessage]
                        })

                        // Also send to backend
                        await sendMessage({
                            roomId: selectedRoom.id,
                            content: "Zurno Customer Service has left the chat"
                        }).unwrap()

                    } catch (error) {
                        console.error("Failed to send leave message:", error)
                    }

                    await stopTakingOver({ roomId: selectedRoom.id }).unwrap()
                    setIsStaffResponding(false)
                }

            } else {
                await takeOver({ roomId: selectedRoom.id }).unwrap()
                setIsStaffResponding(true)
                if (ivsRoom) {
                    const request = new SendMessageRequest("Zurno Customer Service has entered the chat");
                    request.attributes = {
                        display_name: "System",
                        avatar_url: "",
                        type: "system",
                    }

                    try {
                        const response = await ivsRoom.sendMessage(request)
                        const { id: messageId } = response
                        const now = new Date().toISOString()
                        const systemMessage: ChatbotMessage = {
                            id: messageId,
                            roomId: selectedRoom.id,
                            content: "Zurno Customer Service has entered the chat",
                            role: "system",
                            sentAt: now,
                            userName: "System",
                            avatarUrl: "",
                            pending: true,
                            ivsChatMessageId: messageId
                        }

                        setMessages((prev) => {
                            const exists = prev.some((message) => getMessageKey(message) === getMessageKey(systemMessage))
                            return exists ? prev : [...prev, systemMessage]
                        })

                        await sendMessage({
                            roomId: selectedRoom.id,
                            content: "Zurno Customer Service has entered the chat"
                        }).unwrap()

                    } catch (error) {
                        console.error("Failed to send enter message:", error)
                    }
                }
            }
        } catch(err) {
            console.error(err)
        }
    }

    const fetchChats = async (roomId: string, page: number, limit: number) => {
        if (currentRoomIdRef.current !== roomId) {
            return
        }

        setChatLoading(true)
        try {
            if (scrollRef.current) {
                previousScrollHeight.current = scrollRef.current.scrollHeight
            }

            const response = await getChatMessages(
                {id: roomId, params: { page: page, limit: limit }},
            ).unwrap()

            if (currentRoomIdRef.current !== roomId) {
                return
            }

            setMessages(prev => {
                const incomingMessages = response.messages.map(toChatMessage)
                const combined = [...incomingMessages, ...prev]
                return deduplicateMessages(combined)
            })

            if (response.nextPageUrl === null) {
                setHasMoreChatPage(false)
            }

        } catch (error) {
            if (currentRoomIdRef.current === roomId) {
                setChatError('An error occurred while fetching data')
            }
        } finally {
            setChatLoading(false)
        }
    }

    useEffect(() => {
        if (!selectedRoom || isLoadingRoom || selectedRoom.id !== currentRoomIdRef.current) return
        fetchChats(selectedRoom.id, chatPage, CHAT_PAGE_SIZE)
    }, [chatPage, selectedRoom?.id]);

    const chatObserver = useRef<IntersectionObserver | null>(null)

    // Observer for the loading indicator at the top
    const loadMoreObserverRef = useCallback(
        (node: HTMLDivElement) => {
            if (chatLoading) return
            if (chatObserver.current) chatObserver.current.disconnect()
            chatObserver.current = new IntersectionObserver(
                (entries) => {
                    if (entries[0].isIntersecting && hasMoreChatPage) {
                        setChatPage((prevPage) => prevPage + 1)
                    }
                },
                {
                    threshold: 0.5,
                    root: scrollRef.current,
                    rootMargin: '20px 0px 0px 0px'
                }
            )
            if (node) chatObserver.current.observe(node)
        },
        [chatLoading, hasMoreChatPage]
    )

    const { data: fetched, isFetching: messagesLoading, error: messagesError} =
        useGetChatMessagesQuery(
            { id: roomId, params: {page: 1, limit: CHAT_PAGE_SIZE} },
            { skip: !roomId,  refetchOnFocus: true, refetchOnReconnect: true }
        )

    // mutations
    const [sendMessage, { isLoading: isSending }] = useCreateChatBotMessageMutation()
    const [annotate, { isLoading: isAnnotating }] = useAnnotateChatMessageMutation()
    const canSend = !!selectedRoom && (
        isStaffResponding || selectedRoom.adminId === currentAdminId
    )

    const toChatMessage = (message: any): ChatbotMessage => ({
        roomId: message.roomId,
        id:      message.id,
        content: message.content ?? "",
        role:    (message.role ?? "system") as ChatbotMessage["role"],
        sentAt:  message.sentAt,
        assistantId: message.assistantId ?? null,
        adminId:     message.adminId ?? null,
        attributes:  message.attributes ?? {},
        annotatedChatMessageId: message.annotatedChatMessageId ?? null,
        _localAnnotation: message._localAnnotation,
        userName: message.userName,
        avatarUrl: message.avatarUrl ?? "",
        ivsChatMessageId: message.ivsChatMessageId,
        userId: message.userId ?? null,
    })

    const annotations = useMemo(() => {
        const map: Record<string, string> = {}
        messages.forEach(message => {
            if ( message.annotatedChatMessageId) {
                map[message.annotatedChatMessageId] = message.content
            }
        })
        return map
    }, [messages])

    const visibleMessages = messages.filter(message => message.annotatedChatMessageId === null)

    useEffect(() => {
        if (!roomId || selectedRoom?.id === roomId) return
        const roomPayload = fetchedRoom ?? ({ id: roomId } as PartialRoom)
        mountRoom(roomPayload as ChatbotRoom)
    }, [roomId, selectedRoom])

    useEffect(() => {
        if (!fetched || currentRoomIdRef.current !== roomId) return

        const incomingMessages = fetched.messages
            .filter(message => message.content?.trim())
            .map(toChatMessage)

        setMessages(prev => {
            if (chatPage === 1) {
                const mergedMessages = [...prev]

                incomingMessages.forEach(messageFromApi => {
                    const optimisticIndex = mergedMessages.findIndex(
                        message => message.ivsChatMessageId && message.ivsChatMessageId === messageFromApi.ivsChatMessageId
                    )
                    if (optimisticIndex !== -1) {
                        mergedMessages[optimisticIndex] = { ...messageFromApi, pending: false }
                    } else {
                        const existsIndex = mergedMessages.findIndex(
                            message => getMessageKey(message) === getMessageKey(messageFromApi)
                        )
                        if (existsIndex === -1) {
                            mergedMessages.push(messageFromApi)
                        }
                    }
                })

                setShouldScrollToBottom(true)
                const sorted = mergedMessages.sort((a,b) => toMillis(a.sentAt) - toMillis(b.sentAt))
                return deduplicateMessages(sorted)
            }

            return prev
        })

    }, [fetched, roomId])

    useEffect(() => {
        if (!shouldScrollToBottom) return
        scrollRef.current?.scrollTo({ top: scrollRef.current.scrollHeight, behavior: 'smooth' })
        setShouldScrollToBottom(false)
    }, [messages, shouldScrollToBottom])

    const selectRoom = async (room: ChatbotRoom) => {
        if (room.id !== roomId) {
            navigate(`/chatbot/${room.id}`, { replace: false });
        }
        await mountRoom(room)
    }

    const ivsChatMessageToChatbot = (
        ivsChatMessage: IvsChatMessage,
        roomId: string,
    ): ChatbotMessage => {
        const { id, content, sendTime, sender, attributes } = ivsChatMessage
        let role: ChatbotMessage["role"]
        let displayName: string
        switch (sender.attributes?.type) {
            case "assistant":
                role = "bot"
                displayName =  sender.attributes?.role ?? "bot"
                break
            case "user":
                role = "user"
                const firstName = sender.attributes?.firstName?.trim()
                const lastName = sender.attributes?.lastName?.trim()
                displayName = (firstName || lastName)
                    ? `${firstName ?? ''} ${lastName ?? ''}`.trim()
                    : sender.attributes?.display_name
                    ?? sender.attributes?.email
                    ?? "Unknown sender"
                break
            case "admin":
                role = "staff"
                displayName = attributes?.display_name ?? "Unknown sender"
                break
            default:
                role = "system"
                displayName = "Unknown sender"
        }

        const avatarUrl   = sender.attributes?.avatar_url ?? ""
        const userId = sender.userId

        return {
            id: id,
            roomId,
            content,
            role,
            sentAt: sendTime.toISOString(),
            userName: displayName,
            avatarUrl,
            attributes,
            assistantId: null,
            adminId:   role === "staff" ? currentAdminId : null,
            annotatedChatMessageId: null,
            ivsChatMessageId: id,
            userId: role === "user" ? userId : null
        };
    };

    useEffect(() => {
        if (!ivsRoom || !selectedRoom) return
        const unsubscribe = ivsRoom.addListener('message', (incomingMessage) => {
            setMessages((prev) => {
                const chatbotMessage = ivsChatMessageToChatbot(incomingMessage, selectedRoom.id)

                const existingIndex = prev.findIndex(message =>
                    getMessageKey(message) === getMessageKey(chatbotMessage)
                )

                if (existingIndex !== -1) {
                    const clone = [...prev]
                    clone[existingIndex] = {...chatbotMessage, pending: false}
                    return clone
                }

                return [...prev, chatbotMessage]
            })
            setShouldScrollToBottom(true)
        })

        return () => { unsubscribe() }
    }, [ivsRoom, selectedRoom?.id]);

    useEffect(() => {
        if (scrollRef.current && previousScrollHeight.current > 0 && chatPage > 1) {
            const container = scrollRef.current
            const newScrollHeight = container.scrollHeight
            const scrollDiff = newScrollHeight - previousScrollHeight.current
            const bufferOffset = 100
            container.scrollTop = container.scrollTop + scrollDiff + bufferOffset
            previousScrollHeight.current = 0
        }
    }, [messages, chatPage])

    const pushAndSend = async () => {
        if (!input.trim() || !roomId) return

        const draft = input.trim()
        setInput("")

        try {
            if (ivsRoom) {
                const request = new SendMessageRequest(draft);
                request.attributes = {
                    display_name: store.getState().auth.user?.name ?? "Staff",
                    avatar_url:  "", //TODO: Get Admin AVATAR URL
                    type: "staff",
                }
                ivsRoom.sendMessage(request)
                    .then((response) => {
                        const {id: messageId} = response
                        const now = new Date().toISOString()
                        const optimistic : ChatbotMessage = {
                            id: messageId,
                            roomId,
                            content: draft,
                            role: "staff",
                            sentAt: now,
                            userName: currentAdmin?.username ?? "Unknown Staff",
                            avatarUrl: "",
                            pending: true,
                            ivsChatMessageId: messageId
                        }
                        setMessages((prev) => {
                            const exists = prev.some((message) => getMessageKey(message) === getMessageKey(optimistic))
                            return exists ? prev : [...prev, optimistic]
                        })
                    })
                    .catch((error) => {
                        console.error(error)
                    })

                await sendMessage({ roomId, content: draft }).unwrap()
            }

        } catch (error) {
            console.error(error)
            setInput(draft)
        }
    }

    const beginAnnotate = (id: string) => {
        setAnnotatingId(id)
        setAnnotationText("")
    }

    const commitAnnotation = async () => {
        if (!roomId || !annotatingId || !annotationText.trim()) return
        try {
            await annotate({ roomId, originalMessageId: annotatingId, content: annotationText.trim() }).unwrap()
            setMessages(prev => prev.map(message =>
                message.id === annotatingId ? { ...message, _localAnnotation: annotationText.trim() } : message
            ))
        } catch (error) {
            console.error(error)
        } finally {
            setAnnotatingId(null)
            setAnnotationText("")
        }
    }

    const cancelAnnotation = ()=> {
        setAnnotatingId(null)
        setAnnotationText("")
    }

    const saveAnnotation = async () => {
        if (!annotationText.trim() || isAnnotating) return
        try {
            await commitAnnotation()
        } catch (err) {
            console.error("Annotation failed", err)
        }
    }

    const groupByDate = <T,>(
        items: T[],
        getDate: (item: T) => string | undefined
    ): Record<string, T[]> => {
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        const MS_IN_DAY = 86_400_000

        return items.reduce<Record<string, T[]>>((lbls, item) => {
            const time = getDate(item)
            const date  = time ? new Date(time) : new Date(0)
            const day = new Date(date.getFullYear(), date.getMonth(), date.getDate())
            const difference = today.getTime() - day.getTime()

            let label: string
            if      (difference === 0)           label = "Today"
            else if (difference === MS_IN_DAY)   label = "Yesterday"
            else {
                label = date.toLocaleDateString(undefined, {
                    year:  "numeric",
                    month: "short",
                    day:   "numeric",
                })
            }
            (lbls[label] ??= []).push(item)
            return lbls
        }, {})
    }

    const chatMessagesByDate = useMemo(
        () => {
            const messagesInRoom = visibleMessages.filter(message => message.roomId === selectedRoom?.id)
            const groupedMessages = groupByDate(messagesInRoom, message => message.sentAt)
            return Object
                .entries(groupedMessages)
                .map(([label, messages]) => ({
                    label,
                    messages: messages,
                    timestamp: Math.max(...messages.map(message => toMillis(message.sentAt))),
                }))
                .sort((a, b) => a.timestamp - b.timestamp)
        },
        [visibleMessages, selectedRoom?.id]
    )

    return (
        <Fragment>
            {/*=====,======== TOP-LEVEL WRAPPER ============= */}
            <div className="d-flex w-100" style={{height: "100vh", }}>
                {/* Chat Content Area */}
                <div className="flex-grow-1 main-chart-wrapper pt-0 p-2 gap-2 d-lg-flex" style={{minWidth: 0}}>

                    {/* ---------- LEFT SIDEBAR : ROOMS / CONTACTS ---------- */}
                    <div className="chat-info border flex-shrink-0 d-flex flex-column"
                         style={{width: "300px", minWidth: "300px", maxHeight: "100vh"}}>

                        <ChatRoomsList  selectedRoom={selectedRoom}
                                        onRoomSelect={selectRoom}
                                        roomsPageSize={ROOMS_PAGE_SIZE}
                        />
                    </div>

                    {/* ---------- MAIN CHAT AREA ---------- */}
                    <div className="main-chat-area border flex-grow-1 d-flex flex-column" style={{minWidth: 0}}>

                        {/* header */}
                        <div className="d-flex align-items-center justify-content-between p-3 border-bottom">
                            <h6 className="mb-0">
                                {selectedRoom ? (
                                    <div className="d-flex flex-column">
                                        <span className="fw-semibold"> {selectedRoom.userName} </span>
                                    </div>
                                ) : (
                                    <h6 className="mb-0 text-muted">Select a room to start</h6>
                                )
                                }
                            </h6>

                            {selectedRoom && (
                                <div className="d-flex gap-2">
                                    <Button
                                        variant="primary"
                                        size="sm"
                                        disabled={creatingBotChat}
                                        onClick={chatWithBot}
                                    >
                                        <i className="ri-robot-line me-1" />
                                        Chat with bot
                                    </Button>
                                    {selectedRoom.adminId !== currentAdminId && (
                                        <Button
                                            variant={isStaffResponding ? "secondary" : "success"}
                                            size="sm"
                                            disabled={takingOver || stopping}
                                            onClick={toggleTakeOver}
                                        >
                                            {isStaffResponding ? "Stop Taking Over" : "Take Over"}
                                        </Button>
                                    )}

                                    <Button
                                        variant="outline-primary"
                                        size="sm"
                                        onClick={refreshRoom}
                                        disabled={chatLoading || messagesLoading}
                                        title="Refresh messages"
                                    >
                                        <i className="ri-refresh-line me-1"/>
                                        Refresh
                                    </Button>

                                </div>)}
                        </div>

                        {/* messages list */}
                        <div ref={scrollRef} className="flex-grow-1 overflow-auto p-3">
                            {!selectedRoom && (
                                <p className="text-center text-muted mb-0">
                                    Choose a chat room on the left
                                </p>
                            )}

                            {(selectedRoom && (messagesLoading || isLoadingRoom) && !messages.length) && (
                                <div className="text-center py-4">
                                    <Spinner animation="border" size="sm"/>
                                </div>
                            )}

                            {messagesError && selectedRoom ? (
                                <Alert variant="danger" className="m-3"> Couldn't load messages.</Alert>
                            ) : null}

                            {/* Load More Indicator at the top */}
                            {selectedRoom && hasMoreChatPage && (
                                <div
                                    ref={loadMoreObserverRef}
                                    className="text-center py-3"
                                >
                                    {chatLoading ? (
                                        <div className="d-flex align-items-center justify-content-center">
                                            <Spinner animation="border" size="sm" className="me-2"/>
                                            <small className="text-muted">Loading older messages...</small>
                                        </div>
                                    ) : (
                                        <small className="text-muted">Scroll up to load more messages</small>
                                    )}
                                </div>
                            )}

                            <ListGroup variant="flush">
                                {chatMessagesByDate.map(({label, messages}) => (
                                    <Fragment key={label}>
                                        <ListGroup.Item className="text-center bg-transparent border-0 py-1">
                                            <small
                                                className="text-muted text-uppercase d-inline-block px-2 py-1 rounded-2e"
                                                style={{
                                                    backgroundColor: 'rgba(114, 105, 239, 0.15)',
                                                    color: '#7269ef',
                                                    fontSize: '0.75rem'
                                                }}>
                                                {label}
                                            </small>
                                        </ListGroup.Item>
                                        {messages.map((message) => (
                                            <Fragment key={message.id}>
                                                <div className={"border-b py-4"}>
                                                    <ChatBubble
                                                        message={message}
                                                        annotation={annotations[message.id]}
                                                        onDoubleClick={
                                                            message.role === "bot"
                                                                ? () => {
                                                                    if (message.id === "NULL") {
                                                                        alert(
                                                                            "This message hasn't been saved in the DB yet.\nPress the Refresh button once it appears, then try annotating again."
                                                                        )
                                                                    } else {
                                                                        beginAnnotate(message.id)
                                                                    }
                                                                }
                                                                :undefined
                                                        }
                                                    />
                                                </div>

                                                {/* Inline annotation editor */}
                                                {annotatingId === message.id && (
                                                    <Form
                                                        className="my-2"
                                                        onSubmit={(e) => {
                                                            e.preventDefault();
                                                            commitAnnotation();
                                                        }}
                                                    >
                                                        <InputGroup>
                                                            <Form.Control
                                                                placeholder="Add annotation…"
                                                                value={annotationText}
                                                                onChange={(e) => setAnnotationText(e.target.value)}
                                                                disabled={isAnnotating}
                                                                onKeyDown={(e) => {
                                                                    if (e.key === "Enter") {
                                                                        e.preventDefault()
                                                                        commitAnnotation()
                                                                    } else if (e.key === "Escape") {
                                                                        e.preventDefault()
                                                                        setAnnotatingId(null)
                                                                    }
                                                                }}
                                                            />
                                                            <Button
                                                                type="button"
                                                                variant="primary"
                                                                disabled={isAnnotating || !annotationText.trim()}
                                                                onClick={saveAnnotation}
                                                            >
                                                                Save
                                                            </Button>
                                                            <Button
                                                                type="button"
                                                                variant="secondary"
                                                                disabled={isAnnotating}
                                                                onClick={cancelAnnotation}
                                                            >
                                                                Cancel
                                                            </Button>
                                                        </InputGroup>
                                                    </Form>
                                                )}
                                            </Fragment>
                                        ))}
                                    </Fragment>
                                ))}
                            </ListGroup>

                            {chatError && <p className="text-center text-danger py-4">{chatError}</p>}

                        </div>

                        {/* footer / compose box */}
                        <div className="border-top p-3 d-flex gap-2">
                            <Form.Control
                                placeholder="Type a message…"
                                value={input}
                                onChange={(e) => setInput(e.target.value)}
                                disabled={!canSend}
                                onKeyDown={(e) => {
                                    if (e.key === "Enter" && !e.shiftKey && canSend) {
                                        e.preventDefault()
                                        pushAndSend()
                                    }
                                }}
                            />
                            <Button onClick={pushAndSend} disabled={!input.trim() || !canSend || isSending}>
                                <i className="ri-send-plane-2-line me-1"/> Send
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </Fragment>
    )
}

export default Chatbot;
